/**
 * 图像编辑工具类型定义
 * 遵循 Tauri 开发规范的类型安全设计
 */

// 图像编辑API配置
export interface ImageEditingConfig {
  api_url: string;
  api_key: string;
  model_id: string;
  timeout: number;
  max_retries: number;
  retry_delay: number;
}

// 图像编辑请求参数
export interface ImageEditingRequest {
  model: string;
  prompt: string;
  image: string; // Base64编码或URL
  response_format?: string; // "url" 或 "b64_json"
  size?: string; // "adaptive"
  seed?: number;
  guidance_scale?: number;
  watermark?: boolean;
}

// 图像编辑响应数据
export interface ImageEditingResponseData {
  url?: string;
  b64_json?: string;
}

// 图像编辑使用量信息
export interface ImageEditingUsage {
  generated_images: number;
  output_tokens: number;
  total_tokens: number;
}

// 图像编辑错误信息
export interface ImageEditingError {
  code: string;
  message: string;
}

// 图像编辑API响应
export interface ImageEditingResponse {
  model: string;
  created: number;
  data: ImageEditingResponseData[];
  usage: ImageEditingUsage;
  error?: ImageEditingError;
}

// 图像编辑任务状态
export enum ImageEditingTaskStatus {
  Pending = 'Pending',
  Processing = 'Processing',
  Completed = 'Completed',
  Failed = 'Failed',
  Cancelled = 'Cancelled',
}

// 图像编辑任务
export interface ImageEditingTask {
  id: string;
  input_image_path: string;
  output_image_path?: string;
  prompt: string;
  status: ImageEditingTaskStatus;
  progress: number; // 0.0 - 1.0
  error_message?: string;
  request_params: ImageEditingRequest;
  response_data?: ImageEditingResponse;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

// 批量图像编辑任务
export interface BatchImageEditingTask {
  id: string;
  input_folder_path: string;
  output_folder_path: string;
  prompt: string;
  total_images: number;
  processed_images: number;
  successful_images: number;
  failed_images: number;
  status: ImageEditingTaskStatus;
  progress: number; // 0.0 - 1.0
  individual_tasks: ImageEditingTask[];
  request_params: ImageEditingRequest;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

// 图像编辑参数配置
export interface ImageEditingParams {
  guidance_scale: number;
  seed: number;
  watermark: boolean;
  response_format: string;
  size: string;
}

// 默认参数配置
export const DEFAULT_IMAGE_EDITING_PARAMS: ImageEditingParams = {
  guidance_scale: 5.5,
  seed: -1,
  watermark: true,
  response_format: 'url',
  size: 'adaptive',
};

// 默认API配置
export const DEFAULT_IMAGE_EDITING_CONFIG: ImageEditingConfig = {
  api_url: 'https://ark.cn-beijing.volces.com/api/v3/images/generations',
  api_key: '',
  model_id: 'doubao-seededit-3-0-i2i-250628',
  timeout: 120,
  max_retries: 3,
  retry_delay: 2,
};

// 任务状态显示配置
export const TASK_STATUS_CONFIG = {
  [ImageEditingTaskStatus.Pending]: {
    label: '等待中',
    color: 'gray',
    icon: 'Clock',
  },
  [ImageEditingTaskStatus.Processing]: {
    label: '处理中',
    color: 'blue',
    icon: 'Loader',
  },
  [ImageEditingTaskStatus.Completed]: {
    label: '已完成',
    color: 'green',
    icon: 'CheckCircle',
  },
  [ImageEditingTaskStatus.Failed]: {
    label: '失败',
    color: 'red',
    icon: 'XCircle',
  },
  [ImageEditingTaskStatus.Cancelled]: {
    label: '已取消',
    color: 'orange',
    icon: 'Ban',
  },
};

// 预设提示词
export const PRESET_PROMPTS = [
  {
    category: '风格转换',
    prompts: [
      '转换为水彩画风格',
      '转换为油画风格',
      '转换为卡通动漫风格',
      '转换为素描风格',
      '转换为复古胶片风格',
    ],
  },
  {
    category: '场景变换',
    prompts: [
      '改为春天的樱花背景',
      '改为夏日海滩背景',
      '改为秋天的枫叶背景',
      '改为冬日雪景背景',
      '改为城市夜景背景',
    ],
  },
  {
    category: '色彩调整',
    prompts: [
      '增强色彩饱和度',
      '转换为黑白照片',
      '增加暖色调',
      '增加冷色调',
      '提高亮度和对比度',
    ],
  },
  {
    category: '特效处理',
    prompts: [
      '添加光晕效果',
      '添加雨滴效果',
      '添加阳光透射效果',
      '添加星空效果',
      '添加梦幻光斑效果',
    ],
  },
];

// 参数配置选项
export const GUIDANCE_SCALE_OPTIONS = [
  { value: 1.0, label: '1.0 - 最低引导' },
  { value: 2.5, label: '2.5 - 低引导' },
  { value: 5.5, label: '5.5 - 默认引导' },
  { value: 7.5, label: '7.5 - 高引导' },
  { value: 10.0, label: '10.0 - 最高引导' },
];

export const RESPONSE_FORMAT_OPTIONS = [
  { value: 'url', label: 'URL链接' },
  { value: 'b64_json', label: 'Base64编码' },
];

export const SIZE_OPTIONS = [
  { value: 'adaptive', label: '自适应尺寸' },
];

// 工具函数类型
export interface ImageEditingUtils {
  formatTaskStatus: (status: ImageEditingTaskStatus) => string;
  formatProgress: (progress: number) => string;
  formatDuration: (startTime: string, endTime?: string) => string;
  validatePrompt: (prompt: string) => boolean;
  generateTaskId: () => string;
}

// 事件类型
export interface TaskProgressEvent {
  taskId: string;
  progress: number;
  status: ImageEditingTaskStatus;
  message?: string;
}

export interface BatchProgressEvent {
  batchId: string;
  totalImages: number;
  processedImages: number;
  successfulImages: number;
  failedImages: number;
  progress: number;
  currentTask?: string;
}

// 文件选择配置
export interface FileSelectionConfig {
  allowedExtensions: string[];
  maxFileSize: number; // bytes
  multiple: boolean;
}

export const IMAGE_FILE_CONFIG: FileSelectionConfig = {
  allowedExtensions: ['jpg', 'jpeg', 'png'],
  maxFileSize: 10 * 1024 * 1024, // 10MB
  multiple: false,
};

export const BATCH_FOLDER_CONFIG = {
  supportedFormats: ['jpg', 'jpeg', 'png'],
  maxFilesPerBatch: 100,
  maxTotalSize: 500 * 1024 * 1024, // 500MB
};

// API调用函数类型
export interface ImageEditingAPI {
  setConfig: (config: ImageEditingConfig) => Promise<void>;
  setApiKey: (apiKey: string) => Promise<void>;
  editSingleImage: (
    inputPath: string,
    outputPath: string,
    prompt: string,
    params: ImageEditingParams
  ) => Promise<string>;
  createTask: (
    inputPath: string,
    outputPath: string,
    prompt: string,
    params: ImageEditingParams
  ) => Promise<string>;
  executeTask: (taskId: string) => Promise<void>;
  getTaskStatus: (taskId: string) => Promise<ImageEditingTask>;
  editBatchImages: (
    inputFolder: string,
    outputFolder: string,
    prompt: string,
    params: ImageEditingParams
  ) => Promise<string>;
  createBatchTask: (
    inputFolder: string,
    outputFolder: string,
    prompt: string,
    params: ImageEditingParams
  ) => Promise<string>;
  getBatchTaskStatus: (taskId: string) => Promise<BatchImageEditingTask>;
  getAllTasks: () => Promise<ImageEditingTask[]>;
  getAllBatchTasks: () => Promise<BatchImageEditingTask[]>;
  clearCompletedTasks: () => Promise<void>;
  cancelTask: (taskId: string) => Promise<void>;
}
