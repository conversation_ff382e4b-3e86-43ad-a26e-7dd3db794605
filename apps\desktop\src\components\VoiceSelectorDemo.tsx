import React, { useState } from 'react';
import { Volume2, Settings, Users } from 'lucide-react';
import { VoiceSelector } from './VoiceSelector';
import { VoiceInfo } from '../types/voiceClone';
import { SystemVoice } from '../types/systemVoice';

/**
 * 音色选择器演示组件
 * 展示新的音色选择器的使用方法和效果
 */
export const VoiceSelectorDemo: React.FC = () => {
  const [showSelector, setShowSelector] = useState(false);
  const [selectedVoice, setSelectedVoice] = useState<{
    id: string;
    name: string;
    source: 'system' | 'custom';
    data: VoiceInfo | SystemVoice;
  } | null>(null);

  const handleVoiceSelect = (voiceId: string, voiceInfo: VoiceInfo | SystemVoice, source: 'system' | 'custom') => {
    const voiceName = 'voice_name' in voiceInfo ? voiceInfo.voice_name : voiceInfo.name;
    
    setSelectedVoice({
      id: voiceId,
      name: voiceName || voiceId,
      source,
      data: voiceInfo
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">音色选择器演示</h1>
          
          <div className="space-y-6">
            {/* 当前选择的音色 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                当前选择的音色
              </label>
              
              {selectedVoice ? (
                <div className="p-4 border border-gray-200 rounded-lg bg-gray-50">
                  <div className="flex items-center gap-3">
                    {selectedVoice.source === 'system' ? (
                      <Settings className="w-5 h-5 text-blue-600" />
                    ) : (
                      <Users className="w-5 h-5 text-orange-600" />
                    )}
                    <div>
                      <p className="font-medium text-gray-900">{selectedVoice.name}</p>
                      <p className="text-sm text-gray-500">
                        {selectedVoice.source === 'system' ? '系统音色' : '自定义音色'} - ID: {selectedVoice.id}
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 text-center text-gray-500">
                  尚未选择音色
                </div>
              )}
            </div>

            {/* 选择音色按钮 */}
            <div>
              <button
                onClick={() => setShowSelector(true)}
                className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Volume2 className="w-5 h-5" />
                选择音色
              </button>
            </div>

            {/* 功能说明 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-medium text-blue-900 mb-2">新音色选择器特性</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• 🎵 统一展示系统音色和自定义音色</li>
                <li>• 🔍 支持搜索和分类筛选</li>
                <li>• 🎧 音色预览试听功能（开发中）</li>
                <li>• 📱 响应式网格布局</li>
                <li>• ⭐ 收藏常用音色</li>
                <li>• 🏷️ 音色标签和描述</li>
                <li>• 🎨 美观的卡片式设计</li>
              </ul>
            </div>

            {/* 对比说明 */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="font-medium text-green-900 mb-2">相比原版的改进</h3>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• ✅ 简化了主界面，只需一个按钮</li>
                <li>• ✅ 无需在系统音色和自定义音色间切换</li>
                <li>• ✅ 更直观的音色信息展示</li>
                <li>• ✅ 更好的用户体验和交互</li>
                <li>• ✅ 节省了界面空间</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* 音色选择器 */}
      <VoiceSelector
        isOpen={showSelector}
        onClose={() => setShowSelector(false)}
        onSelect={handleVoiceSelect}
        selectedVoiceId={selectedVoice?.id}
      />
    </div>
  );
};
